<template>
	<div class="tag-selector">
		<!-- 标签显示区域 -->
		<div class="tags-container-combined">
			<div class="tags-row" v-if="selectedTags.length > 0">
				<span class="tag-category-label">帖子类型</span>
				<div class="tags-group">
					<div
						v-for="(tag, index) in selectedTags"
						:key="tag.id"
						class="custom-tag-display"
						:style="getTagDisplayStyle(tag, index)"
						@click="removeTag(tag)"
					>
						{{ tag.name }}
						<span class="tag-close">×</span>
					</div>
				</div>
			</div>
			<div class="tags-row" v-if="selectedSchoolTags.length > 0">
				<span class="tag-category-label">学校名称</span>
				<div class="tags-group">
					<div
						v-for="(tag, index) in selectedSchoolTags"
						:key="tag.id"
						class="custom-tag-display school-tag-display"
						:style="getTagDisplayStyle(tag, index)"
						@click="removeSchoolTag(tag, index)"
					>
						{{ tag.name }}
						<span class="tag-close">×</span>
					</div>
				</div>
			</div>
			<div class="tags-row" v-if="selectedMajorTags.length > 0">
				<span class="tag-category-label">专业方向</span>
				<div class="tags-group">
					<div
						v-for="(tag, index) in selectedMajorTags"
						:key="tag.id"
						class="custom-tag-display"
						:style="getTagDisplayStyle(tag, index)"
						@click="removeMajorTag(tag)"
					>
						{{ tag.name }}
						<span class="tag-close">×</span>
					</div>
				</div>
			</div>
			<el-button
				class="button-new-tag"
				size="small"
				@click="showTagDialog"
			>
				+ 添加标签
			</el-button>
		</div>

		<!-- 标签选择弹窗 -->
		<el-dialog
			v-model="tagDialogVisible"
			title="选择标签"
			width="900px"
			:close-on-click-modal="false"
		>
			<div class="tag-dialog-content">
				<div class="tag-title">添加帖子类型标签</div>
				<div class="tag-list">
					<div class="custom-tag-grid">
						<template v-for="(item, index) in processedTags" :key="item.id">
							<!-- 分隔符 -->
							<div
								v-if="item.type === 'separator'"
								:class="item.breakType === 'double' ? 'double-break' : 'single-break'"
								class="tag-separator"
							></div>

							<!-- 标签 -->
							<div
								v-else-if="item.type === 'tag'"
								class="custom-tag-item"
								:class="{ 'selected': tempSelectedTagIds.includes(item.id) }"
								:style="getTagDialogStyle(item, index, tempSelectedTagIds.includes(item.id))"
								@click="toggleTagSelection(item.id)"
							>
								{{ item.name }}
							</div>
						</template>
					</div>
				</div>

				<div class="tag-title" style="margin-top: 30px;">添加学校名称标签</div>
				<div class="school-tag-section">
					<div class="add-tag">
						<el-select
							v-model="schoolTagInput.id"
							filterable
							remote
							:remote-method="handleSchoolSearch"
							:loading="schoolSearchLoading"
							@change="onSchoolTagChange"
							@clear="onSchoolTagClear"
							@keyup.enter="addSchoolTagsOnEnter"
							placeholder="请输入学校名称标签"
							clearable
							style="width: 100%;"
						>
							<el-option
								v-for="(item, index) in schoolSearchData"
								:key="index"
								:label="item.name + (item.synonyms && item.synonyms.length > 0 ? ',' + item.synonyms.join(',') : '')"
								:value="item.id"
							>
								{{ item.name }}
							</el-option>
						</el-select>
					</div>
					<div class="school-tags-display">
						<div
							v-for="(tag, index) in selectedSchoolTags"
							:key="tag.id"
							class="custom-tag-item school-tag-item"
							:style="getTagDisplayStyle(tag, index)"
						>
							{{ tag.name }}
							<span class="tag-close" @click="removeSchoolTag(tag, index)">×</span>
						</div>
						<p v-if="selectedSchoolTags.length === 0" style="color: #ccc; font-size: 12px; margin: 10px 0;">
							还没有添加学校名称标签...
						</p>
					</div>
				</div>

				<div class="tag-title" style="margin-top: 30px;">添加专业方向标签</div>
				<div class="tag-list">
					<div class="custom-tag-grid">
						<template v-for="(item, index) in processedMajorTags" :key="item.id">
							<!-- 分隔符 -->
							<div
								v-if="item.type === 'separator'"
								:class="item.breakType === 'double' ? 'double-break' : 'single-break'"
								class="tag-separator"
							></div>

							<!-- 标签 -->
							<div
								v-else-if="item.type === 'tag'"
								class="custom-tag-item"
								:class="{ 'selected': tempSelectedMajorTagIds.includes(item.id) }"
								:style="getTagDialogStyle(item, index, tempSelectedMajorTagIds.includes(item.id))"
								@click="toggleMajorTagSelection(item.id)"
							>
								{{ item.name }}
							</div>
						</template>
					</div>
				</div>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancelTagSelection">取消</el-button>
					<el-button type="primary" @click="confirmTagSelection">
						确定 (帖子类型: {{ tempSelectedTagIds.length }} 个, 专业方向: {{ tempSelectedMajorTagIds.length }} 个)
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { onMounted, watch, nextTick } from "vue";
import { useTagManagement } from "../composables/useTagManagement.js";

// Props
interface Props {
	modelValue?: {
		tags?: string;
		tagsVal?: string;
		majorTags?: string;
		majorTagsVal?: string;
		schoolTags?: string;
		schoolTagsVal?: string;
	};
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: () => ({})
});

// Emits
const emit = defineEmits<{
	'update:modelValue': [value: any];
	'tagsChange': [tags: any];
}>();

// 使用标签管理composable
const {
	// 数据
	availableTags,
	selectedTags,
	processedTags,
	availableMajorTags,
	selectedMajorTags,
	processedMajorTags,
	selectedSchoolTags,
	schoolSearchData,
	schoolSearchLoading,
	schoolTagInput,
	tagDialogVisible,
	tempSelectedTagIds,
	tempSelectedMajorTagIds,
	
	// 方法
	loadAvailableTags,
	loadAvailableMajorTags,
	handleSchoolSearch,
	showTagDialog,
	cancelTagSelection,
	confirmTagSelection: originalConfirmTagSelection,
	toggleTagSelection,
	toggleMajorTagSelection,
	removeTag: originalRemoveTag,
	removeMajorTag: originalRemoveMajorTag,
	removeSchoolTag: originalRemoveSchoolTag,
	onSchoolTagChange: originalOnSchoolTagChange,
	onSchoolTagClear,
	getTagDisplayStyle,
	getTagDialogStyle,
	initSelectedTags,
	initSelectedMajorTags,
	initSelectedSchoolTags
} = useTagManagement();

// 包装方法以发送事件
const confirmTagSelection = () => {
	originalConfirmTagSelection();
	emitTagsChange();
};

const removeTag = (tag: any) => {
	originalRemoveTag(tag);
	emitTagsChange();
};

const removeMajorTag = (tag: any) => {
	originalRemoveMajorTag(tag);
	emitTagsChange();
};

const removeSchoolTag = (tag: any, index: number) => {
	originalRemoveSchoolTag(tag, index);
	emitTagsChange();
};

// 包装学校标签添加方法
const onSchoolTagChange = (tagId: string) => {
	originalOnSchoolTagChange(tagId);
	emitTagsChange();
};

const addSchoolTagsOnEnter = () => {
	if (schoolTagInput.value.id) {
		onSchoolTagChange(schoolTagInput.value.id);
		// emitTagsChange 会在 onSchoolTagChange 内部调用，不需要重复调用
	}
};

// 发送标签变更事件
const emitTagsChange = () => {
	const tagsData = {
		tags: selectedTags.value.map(tag => tag.name).join("|"),
		tagsVal: selectedTags.value.map(tag => tag.id).join("|"),
		majorTags: selectedMajorTags.value.map(tag => tag.name).join("|"),
		majorTagsVal: selectedMajorTags.value.map(tag => tag.id).join("|"),
		schoolTags: selectedSchoolTags.value.map(tag => tag.name).join("|"),
		schoolTagsVal: selectedSchoolTags.value.map(tag => tag.id).join("|")
	};

	// 设置标志防止递归更新
	isInternalUpdate.value = true;
	emit('update:modelValue', tagsData);
	emit('tagsChange', tagsData);

	// 在下一个tick重置标志
	nextTick(() => {
		isInternalUpdate.value = false;
	});
};

// 添加标志防止递归更新
const isInternalUpdate = ref(false);

// 监听props变化，初始化标签
watch(() => props.modelValue, (newValue) => {
	if (newValue && !isInternalUpdate.value) {
		initSelectedTags(newValue.tagsVal, newValue.tags);
		initSelectedMajorTags(newValue.majorTagsVal, newValue.majorTags);
		initSelectedSchoolTags(newValue.schoolTagsVal, newValue.schoolTags);
	}
}, { immediate: true });

// 移除学校标签的自动监听，避免递归更新
// 学校标签的变更会通过具体的操作方法来触发事件

onMounted(() => {
	loadAvailableTags();
	loadAvailableMajorTags();
});
</script>

<style lang="scss" scoped>
@import "../css/tag-selector.scss";
</style>
